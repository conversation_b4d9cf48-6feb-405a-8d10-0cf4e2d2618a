<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getPatrolExecute"></ProTable>
  </div>
</template>

<script setup lang="tsx" name="dailyProfitSharing">
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { getPlanPatrolExecute, Plan, getPlanMaintenanceExecute, statusOptions } from "@/api/modules/plan";
import dayjs from "dayjs";

const route = useRoute();
let getPatrolExecute: typeof getPlanPatrolExecute;
let title = "";
if (route.name === "patrolTaskList") {
  title = "巡检计划";
  getPatrolExecute = getPlanPatrolExecute;
} else {
  title = "维保计划";
  getPatrolExecute = getPlanMaintenanceExecute;
}

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

const columns = reactive<ColumnProps<Plan.PlanRes>[]>([
  {
    prop: "planCode",
    label: "计划编号"
  },
  {
    prop: "code",
    search: { el: "input" },
    label: "任务编号"
  },
  {
    prop: "name",
    label: "任务名称"
  },
  // {
  //   prop: "orgName",
  //   label: "所属院区"
  // },
  {
    label: "设备名称",
    prop: "deviceName"
  },
  {
    label: "关联部门",
    prop: "departments",
    render(scope) {
      return scope.row.departments.map(item => item.name).join("、");
    }
  },
  {
    label: "关联人员",
    prop: "executors",
    render(scope) {
      return scope.row.executors.map(item => item.name).join("、");
    }
  },
  {
    label: "任务生成时间",
    prop: "createdAt",
    render(scope) {
      return dayjs(scope.row.createdAt).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  {
    label: "任务完成时间",
    prop: "completedTime",
    render(scope) {
      return dayjs(scope.row.createdAt).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  { label: "任务状态", prop: "status", enum: statusOptions },
  { label: "情况说明", prop: "planRemark" },
  {
    label: "是否需要报修",
    prop: "isRepair",
    render(scope) {
      return scope.row.isRepair ? "是" : "否";
    }
  },
  {
    label: "关联报修单编号",
    prop: "repairFormCode"
  }
]);
</script>
