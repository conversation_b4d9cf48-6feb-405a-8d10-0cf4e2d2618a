<template>
  <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large">
    <el-form-item prop="phoneNumber">
      <el-input v-model="loginForm.phoneNumber">
        <template #prefix>
          <el-icon class="el-input__icon">
            <user />
          </el-icon>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="password">
      <el-input v-model="loginForm.password" type="password" show-password autocomplete="new-password">
        <template #prefix>
          <el-icon class="el-input__icon">
            <lock />
          </el-icon>
        </template>
      </el-input>
    </el-form-item>
    <!-- 增加一个院区选择框 -->
    <el-form-item prop="hospitalId">
      <el-select v-model="loginForm.hospitalId" placeholder="请选择院区" class="w-full">
        <template #prefix>
          <el-icon class="el-input__icon">
            <OfficeBuilding />
          </el-icon>
        </template>
        <el-option
          v-for="item in hospitalList"
          :key="item.id"
          :value="String(item.id)"
          :label="item.name"
        />
      </el-select>
    </el-form-item>
  </el-form>
  <div class="login-btn">
    <el-button :icon="CircleClose" round size="large" @click="resetForm(loginFormRef)"> 重置 </el-button>
    <el-button :icon="UserFilled" round size="large" type="primary" :loading="loading" @click="login(loginFormRef)">
      登录
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { HOME_URL } from "@/config";
import { getTimeState } from "@/utils";
import { Login, Hospital } from "@/api/interface";
import { getUserInfoApi, loginApi } from "@/api/modules/login";
import { getResourceData } from "@/api/modules/hospital";
import { useUserStore } from "@/stores/modules/user";
import { useTabsStore } from "@/stores/modules/tabs";
import { useKeepAliveStore } from "@/stores/modules/keepAlive";
import { initDynamicRouter } from "@/routers/modules/dynamicRouter";
import { CircleClose, UserFilled, OfficeBuilding } from "@element-plus/icons-vue";
import type { ElForm, FormRules } from "element-plus";

const router = useRouter();
const userStore = useUserStore();
const tabsStore = useTabsStore();
const keepAliveStore = useKeepAliveStore();

type FormInstance = InstanceType<typeof ElForm>;
const loginFormRef = ref<FormInstance>();
const loginRules = reactive<FormRules>({
  phoneNumber: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
  hospitalId: [{ required: true, message: "请选择院区", trigger: "change" }]
});

const loading = ref(false);
const loginForm = reactive<Login.ReqLoginForm>({
  phoneNumber: "",
  password: "",
  hospitalId: ""
});

// 医院数据
const hospitalList = ref<Hospital.ResResourceData[]>([]);

/**
 * 获取医院数据
 */
async function getHospitalList() {
  try {
    const { data } = await getResourceData();
    hospitalList.value = data;
  } catch (error) {
    console.error("获取医院数据失败:", error);
  }
}

// login
const login = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate(async valid => {
    if (!valid) return;
    loading.value = true;
    try {
      // 1.执行登录接口
      const { data } = await loginApi({ ...loginForm, password: loginForm.password });
      userStore.setToken(data.tokenValue);

      getUserInfoApi().then(res => {
        userStore.setUserInfo(res.data);
      });

      // 2.添加动态路由
      await initDynamicRouter();

      // 3.清空 tabs、keepAlive 数据
      tabsStore.setTabs([]);
      keepAliveStore.setKeepAliveName([]);
      // 4.跳转到首页
      router.push(HOME_URL);
      ElNotification({
        title: getTimeState(),
        message: "欢迎登录",
        type: "success",
        duration: 3000
      });
    } finally {
      loading.value = false;
    }
  });
};

// resetForm
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};

onMounted(() => {
  // 获取医院数据
  getHospitalList();

  // 监听 enter 事件（调用登录）
  document.onkeydown = (e: KeyboardEvent) => {
    e = (window.event as KeyboardEvent) || e;
    if (e.code === "Enter" || e.code === "enter" || e.code === "NumpadEnter") {
      if (loading.value) return;
      login(loginFormRef.value);
    }
  };
});
</script>

<style scoped lang="scss">
@use "../index.scss";
</style>
