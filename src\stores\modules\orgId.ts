import { getOrganizationTree, Org } from "@/api/modules/organization";
import { defineStore } from "pinia";

export const useOrgStore = defineStore("useOrgStore", () => {
  /** 组织机构 */
  const { state: orgTree, execute: refreshOrgTree } = useAsyncState(
    async () => {
      const { data } = await getOrganizationTree();
      console.log("组织机构========", data);
      return data;
    },
    [],
    { immediate: false }
  );

  /**
   * 将树形数据扁平化为部门列表
   */
  const departmentList = computed(() => {
    const flattenTree = (nodes: Org.ResOrganizationTree[]): Org.ResOrganizationTree[] => {
      let result: Org.ResOrganizationTree[] = [];

      nodes.forEach(node => {
        // 添加当前节点
        result.push({
          id: node.id,
          name: node.name,
          parentId: node.parentId,
          liaisonName: node.liaisonName,
          liaisonPhoneNumber: node.liaisonPhoneNumber,
          bankAccountNumber: node.bankAccountNumber,
          level: node.level,
          type: node.type,
          status: node.status,
          sort: node.sort,
          childrenList: node.childrenList
        });

        // 递归处理子节点
        if (node.childrenList && node.childrenList.length > 0) {
          result = result.concat(flattenTree(node.childrenList));
        }
      });

      return result;
    };

    return flattenTree(orgTree.value || []);
  });

  /**
   * 医院列表（只包含医院类型的组织）
   */
  const hospitalList = computed(() => {
    return departmentList.value.filter(item => item.level === Org.orgType.hospital);
  });

  const refreshOrg = () => refreshOrgTree();

  return {
    orgTree,
    departmentList,
    hospitalList,
    refreshOrgTree,
    refreshOrg
  };
});
