<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="450px" :title="`${drawerProps.title}机构`">
    <el-form
      ref="ruleFormRef"
      label-width="120px"
      label-suffix=" :"
      :rules="rules"
      :disabled="drawerProps.isView"
      :model="drawerProps.row"
      :hide-required-asterisk="drawerProps.isView"
    >
      <el-form-item label="上级组织机构" prop="parentId" v-if="drawerProps.row.id !== treeRoot">
        <el-tree-select
          :disabled="drawerProps.title !== '新增'"
          v-model="drawerProps.row!.parentId"
          :check-strictly="true"
          :render-after-expand="false"
          node-key="id"
          :props="{
            label: 'name',
            children: 'childrenList'
          }"
          :data="drawerProps.organizationList"
        ></el-tree-select>
      </el-form-item>
      <el-form-item label="组织机构名称" prop="name">
        <el-input v-model="drawerProps.row!.name" placeholder="请填写组织机构名称"></el-input>
      </el-form-item>
      <el-form-item label="类型" prop="type" :required="typeShow" v-show="typeShow">
        <el-select v-model="drawerProps.row!.type" placeholder="请选择类型" :disabled="drawerProps.title !== '新增'">
          <el-option v-for="item in orgTypeOptions" :key="item.value" v-bind="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="联络人名称" prop="liaisonName">
        <el-input v-model="drawerProps.row!.liaisonName" placeholder="请填写联络人名称"></el-input>
      </el-form-item>
      <el-form-item label="联络人电话" prop="liaisonPhoneNumber">
        <el-input v-model="drawerProps.row!.liaisonPhoneNumber" placeholder="请填写联络人电话"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-switch v-model="drawerProps.row!.status" :active-value="1" :inactive-value="0"></el-switch>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="DepartmentDrawer">
import { Org, orgTypeOptions } from "@/api/modules/organization";
import type { FormInstance, FormRules } from "element-plus/es/components/form";

const rules = reactive<FormRules>({
  // parentId: [{ required: true, message: "请选择上级组织机构", trigger: "change" }],
  name: [{ required: true, message: "请填写组织机构名称", trigger: "blur" }],
  liaisonPhoneNumber: [{ message: "请填写正确的电话", trigger: "blur", pattern: /^[\d-]+$/ }]
});

interface DrawerProps {
  title: string;
  isView: boolean;
  organizationList: Org.ResOrganizationTree[];
  row: Partial<Org.ResOrganizationTree>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  organizationList: [],
  row: {}
});

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = true;
};

const treeRoot = "10000001";
const typeShow = computed(() => {
  return drawerProps.value.row.parentId === treeRoot;
});

// 提交数据（新增/编辑）
const ruleFormRef = ref<FormInstance>();
const handleSubmit = () => {
  ruleFormRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await drawerProps.value.api!(drawerProps.value.row);
      ElMessage.success({ message: `${drawerProps.value.title}用户成功！` });
      drawerProps.value.getTableList!();
      drawerVisible.value = false;
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
