import http from "@/api";
import { PORT1 } from "../config/servicePort";
import { ReqPage, ResPage } from "../interface";

export namespace Plan {
  export interface Item {
    id: string;
    name: string;
  }

  export enum CircleType {
    day = 1,
    month = 2
  }

  // 状态（1-生效，2-作废）
  export enum Status {
    // unfinished = 0,
    finished = 1,
    expired = 2
  }

  export interface PlanRes {
    createBy: number;
    updateBy: number;
    createdAt: number;
    updatedAt: number;
    deleted: number;
    id: string;
    code: string;
    isRepair: boolean;
    name: string;
    circleType: CircleType;
    circle: number;
    status: Status;
    remark: string;
    devices: Item[];
    executors: Item[];
    departments: Item[];
  }

  export interface PlanParams {
    name: string;
    circleType: CircleType;
    circle: number;
    remark: string;
    deviceIds: string[];
    orgIds: string[];
    executorIds: string[];
  }
}

export const circleTypeOptions = [
  { label: "天", value: Plan.CircleType.day },
  { label: "月", value: Plan.CircleType.month }
];

export const statusOptions = [
  // { label: "未完成", value: Plan.Status.unfinished },
  { label: "生效", value: Plan.Status.finished },
  { label: "作废", value: Plan.Status.expired }
];

/**
 * @name 巡检计划-列表
 */
export const getPlanPatrolList = (params: ReqPage & { code?: string; name?: string }, loading = true) => {
  return http.get<ResPage<Plan.PlanRes>>(PORT1 + `/project/plan/patrol/paging`, params, { loading });
};

/**
 * @name 巡检计划-查询
 */
export const getPlanPatrolDetail = (id: number | string, loading = true) => {
  return http.get<Plan.PlanRes>(PORT1 + `/project/plan/patrol/detail/${id}`, {}, { loading });
};

/**
 * @name 巡检计划-新增
 * @param params
 * @param loading
 */
export const addPlanPatrol = (params: Partial<Plan.PlanRes>, loading = false) => {
  return http.post(PORT1 + `/project/plan/patrol/create`, params, { loading });
};

/**
 * @name 巡检计划-修改
 */
export const editPlanPatrol = (params: Partial<Plan.PlanRes>, loading = false) => {
  return http.post(PORT1 + `/project/plan/patrol/update`, params, { loading });
};

/**
 * @name 巡检计划-删除
 */
export const deletePlanPatrol = (id: number, loading = true) => {
  return http.delete(PORT1 + `/project/plan/patrol/abandon/${id}`, undefined, { loading });
};

/**
 * @name 巡检计划-执行情况
 */
export const getPlanPatrolExecute = (params: ReqPage & { planId: number }, loading = true) => {
  return http.get(PORT1 + `/project/plan/patrol/task/paging`, params, { loading });
};

/**
 * @name 维保任务-列表
 */
export const getPlanMaintenanceList = (params: ReqPage & { code?: string; name?: string }, loading = true) => {
  return http.get<ResPage<Plan.PlanRes>>(PORT1 + `/project/plan/maintenance/paging`, params, { loading });
};

/**
 * @name 维保计划-查询
 */
export const getPlanMaintenanceDetail = (id: number | string, loading = true) => {
  return http.get<Plan.PlanRes>(PORT1 + `/project/plan/maintenance/detail/${id}`, undefined, { loading });
};

/**
 * @name 维保计划-新增
 * @param params
 * @param loading
 */
export const addPlanMaintenance = (params: Partial<Plan.PlanRes>, loading = false) => {
  return http.post(PORT1 + `/project/plan/maintenance/create`, params, { loading });
};

/**
 * @name 维保计划-修改
 */
export const editPlanMaintenance = (params: Partial<Plan.PlanRes>, loading = false) => {
  return http.post(PORT1 + `/project/plan/maintenance/update`, params, { loading });
};

/**
 * @name 维保计划-删除
 */
export const deletePlanMaintenance = (id: number, loading = true) => {
  return http.delete(PORT1 + `/project/plan/maintenance/abandon/${id}`, undefined, { loading });
};

/**
 * @name 维保计划-执行情况
 */
export const getPlanMaintenanceExecute = (params: ReqPage & { planId: number }, loading = true) => {
  return http.get(PORT1 + `/project/plan/maintenance/task/paging`, params, { loading });
};
