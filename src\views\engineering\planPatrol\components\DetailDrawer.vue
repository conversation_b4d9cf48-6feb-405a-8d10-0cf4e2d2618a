<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="450px" :title="`${drawerProps.title}计划`">
    <el-form ref="ruleFormRef" label-width="160px" label-suffix=" :" @submit="onSubmit" :disabled="drawerProps.isView">
      <el-form-item :error="errors.name" label="计划名称" required>
        <el-input v-bind="formData.name" />
      </el-form-item>
      <el-form-item :error="errors.circleType || errors.circle" label="周期" required>
        <div class="flex flex-nowrap">
          <!-- @vue-ignore -->
          <el-input class="mr-1" v-bind="formData.circle" />
          <el-select class="w-1" v-bind="formData.circleType">
            <el-option v-for="item in circleTypeOptions" :key="item.value" v-bind="item"></el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item :error="errors.orgIds" label="部门" required>
        <el-tree-select
          v-bind="formData.orgIds"
          multiple
          placeholder="请选择部门"
          :data="orgStore.orgTree"
          :render-after-expand="false"
          :check-strictly="true"
          show-checkbox
          node-key="id"
          :props="{
            label: 'name',
            children: 'childrenList'
          }"
          :default-expanded-keys="expandedKeys"
          :default-checked-keys="formData.orgIds.modelValue"
          collapse-tags
          collapse-tags-tooltip
        />
      </el-form-item>
      <el-form-item :error="errors.deviceIds" label="设备" required>
        <el-select
          v-bind="formData.deviceIds"
          value-key="id"
          multiple
          remote
          filterable
          reserve-keyword
          :remote-method="(name: string) => refreshDeviceList(500, name)"
          :loading="deviceListLoading"
        >
          <el-option v-for="item in deviceList" :key="item.id" :value="item.id" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.executorIds" label="人员">
        <el-select
          v-bind="formData.executorIds"
          value-key="id"
          reserve-keyword
          remote
          multiple
          filterable
          :remote-method="(name: string) => refreshExecutorList(500, name)"
          :loading="executorListLoading"
        >
          <el-option v-for="item in executorList" :key="item.id" :value="item.id" :label="item.nickname"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :error="errors.remark" label="备注">
        <el-input v-bind="formData.remark" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="onSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="SparePartsDetailDrawer">
import { useForm } from "vee-validate";
import { toTypedSchema } from "@vee-validate/yup";
import { object, string, number, array } from "yup";
import { Plan, circleTypeOptions } from "@/api/modules/plan";
import { useOrgStore } from "@/stores/modules/orgId";
// import { getDepartmentListByHospitalId } from "@/api/modules/organization";
import { getDeviceList } from "@/api/modules/device";
import { getUserList } from "@/api/modules/user";

interface DrawerProps {
  title?: "新增" | "编辑" | "查看";
  isView: boolean;
  row: Partial<Plan.PlanRes>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    name: string().required().label("计划名称"),
    circleType: number().required().label("周期类型"),
    circle: number()
      .positive()
      .integer()
      .transform(value => (isNaN(value) ? undefined : value))
      .required("巡检周期格式不正确")
      .label("巡检周期"),
    orgIds: array().min(1, "关联部门不能为空").required().label("关联部门"),
    deviceIds: array().min(1, "关联设备不能为空").required().label("关联设备"),
    executorIds: array().label("关联人员"),
    remark: string().nullable().label("备注")
  })
);

const { defineComponentBinds, handleSubmit, errors, setValues, resetForm } = useForm<Plan.PlanParams>({
  validationSchema: schema,
  initialValues: {}
});

const formData = ref({
  name: defineComponentBinds("name", elPlusConfig),
  circleType: defineComponentBinds("circleType", elPlusConfig),
  circle: defineComponentBinds("circle", elPlusConfig),
  orgIds: defineComponentBinds("orgIds", elPlusConfig),
  deviceIds: defineComponentBinds("deviceIds", elPlusConfig),
  executorIds: defineComponentBinds("executorIds", elPlusConfig),
  remark: defineComponentBinds("remark", elPlusConfig)
});

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: undefined,
  row: {}
});

// 组织机构相关
const orgStore = useOrgStore();
const expandedKeys = ref<string[]>([]);

/**
 * 根据选中的部门ID数组获取需要展开的父节点ID
 */
function getExpandedKeys(selectedIds: string[], treeData: any[]): string[] {
  const expandedSet = new Set<string>();

  function findParents(nodes: any[], targetIds: string[], parentId?: string) {
    for (const node of nodes) {
      if (targetIds.includes(node.id) && parentId) {
        expandedSet.add(parentId);
      }

      if (node.childrenList && node.childrenList.length > 0) {
        findParents(node.childrenList, targetIds, node.id);
      }
    }
  }

  findParents(treeData, selectedIds);
  return Array.from(expandedSet);
}

/**
 * 设备列表
 */
const {
  state: deviceList,
  execute: refreshDeviceList,
  isLoading: deviceListLoading
} = useAsyncState(async (name: string) => {
  name = name.trim();
  const params = name
    ? {
        name,
        pageIndex: 1,
        pageSize: 30
      }
    : {
        pageIndex: 1,
        pageSize: 30
      };

  const { data } = await getDeviceList(params, false);
  return data.records;
}, []);

/**
 * 人员列表
 */
const {
  state: executorList,
  execute: refreshExecutorList,
  isLoading: executorListLoading
} = useAsyncState(async (nickname: string) => {
  const { data } = await getUserList(
    {
      nickname,
      pageIndex: 1,
      pageSize: 30
    },
    false
  );
  return data.records;
}, []);

let id: string | undefined;
// 接收父组件传过来的参数
const acceptParams = async (params: DrawerProps) => {
  drawerProps.value = params;

  // 确保组织机构数据已加载
  await orgStore.refreshOrgTree();

  if (params.title !== "新增") {
    // 处理部门数据 - 从departments数组中获取ID
    const orgIds = params.row.departments?.map(item => item.id) || [];

    setValues({
      name: params.row.name,
      circleType: params.row.circleType,
      circle: params.row.circle,
      orgIds: orgIds,
      deviceIds: params.row.devices?.map(item => `${item.id}`),
      executorIds: params.row.executors?.map(item => item.id),
      remark: params.row.remark
    });

    // 如果是编辑模式且有选中的部门，设置展开的父节点
    if (orgIds.length > 0 && orgStore.orgTree.length > 0) {
      expandedKeys.value = getExpandedKeys(orgIds, orgStore.orgTree);
    }

    id = params.row.id;
  } else {
    resetForm();
    expandedKeys.value = [];
    id = undefined;
  }

  drawerVisible.value = true;
};

const onSubmit = handleSubmit(async values => {
  await drawerProps.value.api!({
    id,
    ...values
  });
  drawerVisible.value = false;
  drawerProps.value.getTableList!();
});

defineExpose({
  acceptParams
});
</script>
