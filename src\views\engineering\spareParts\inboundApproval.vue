<template>
  <div class="card content-box">
    <el-form class="w-full" ref="ruleFormRef" label-width="160px" label-suffix=" :" @submit="onSubmit">
      <el-form-item :error="errors.investorType" label="出资方类型" required>
        <el-select v-bind="formData.investorType">
          <el-option v-for="item in InvestorTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
<!--      <el-form-item :error="errors.orgId" label="出资方" required>-->
<!--        <el-select v-bind="formData.orgId">-->
<!--          <el-option v-for="item in orgList" :key="item.id" :label="item.name" :value="item.id" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item :error="errors.entryTime" label="入库时间" required>
        <el-date-picker v-bind="formData.entryTime" type="date" value-format="x" />
      </el-form-item>
      <el-form-item class="block!" label="备件" required>
        <div class="ml-140px">
          <div class="mb-4 flex" v-for="(field, idx) in fields" :key="field.key">
            <!-- @vue-ignore -->
            <el-form-item :error="errors[`partBatchs[${idx}].partBatchId`]" label="名称" label-width="60px">
              <!-- <input type="text" v-model="field.value.partBatchId" /> -->
              <el-select
                v-model="field.value.partBatchId"
                :validate-event="false"
                reserve-keyword
                filterable
                remote
                :loading="isLoading"
                :remote-method="(name: string) => getPartBatch(500, name)"
              >
                <el-option v-for="item in partBatchOptions" :key="item.id" :value="item.id" :label="item.name" />
              </el-select>
            </el-form-item>
            <!-- @vue-ignore -->
            <el-form-item :error="errors[`partBatchs[${idx}].quantity`]" label="数量" label-width="60px">
              <el-input-number v-model="field.value.quantity" :validate-event="false" :min="0" />
            </el-form-item>
            <div class="ml-4">
              <el-button :icon="Minus" type="danger" circle @click="handleRemovePart(idx)"></el-button>
              <el-button :icon="Plus" circle @click="handleAddPart" v-if="idx === fields.length - 1"></el-button>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="附件">
        <UploadFils v-model:file-list="attachmentFiles" :file-size="10">
          <template #tip> 附件大小不能超过 10M </template>
        </UploadFils>
      </el-form-item>
      <el-form-item :error="errors.remark" label="备注">
        <el-input v-bind="formData.remark" type="textarea" />
      </el-form-item>
      <div class="flex justify-end">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" native-type="submit">确定</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="tsx" name="inboundApproval">
import { useForm, useFieldArray } from "vee-validate";
import { toTypedSchema } from "@vee-validate/yup";
import { object, string, array, number } from "yup";
import {
  SpareParts,
  InvestorTypeOptions,
  getSparePartsBatch,
  createApproval,
  getApprovalDetail,
  updateApproval
} from "@/api/modules/spareParts";
import type { UploadUserFile } from "element-plus";
import { Minus, Plus } from "@element-plus/icons-vue";
import { useOrgStore } from "@/stores/modules/orgId";
import UploadFils from "@/components/Upload/Files.vue";
import { useTabsStore } from "@/stores/modules/tabs";

const orgStore = useOrgStore();

const elPlusConfig = () => ({
  props: {
    validateEvent: false
  }
});

const schema = toTypedSchema(
  object({
    investorType: number().required().label("出资方类型"),
    // orgId: string().required().label("出资方"),
    // investor: string().required().label("出资方名称"),
    entryTime: number().required().label("入库时间"),
    partBatchs: array()
      .of(
        object({
          partBatchId: string().required().label("备件名称"),
          quantity: number().required().label("备件数量")
        })
      )
      .label("备件批次数组"),
    remark: string().label("备注")
  })
);

const { defineComponentBinds, handleSubmit, resetForm, errors, setValues } = useForm<SpareParts.ApprovalParams>({
  validationSchema: schema,
  initialValues: {
    partBatchs: [
      {
        quantity: 0,
        partBatchId: undefined
      }
    ]
  },
  keepValuesOnUnmount: false
});

const { fields, remove, push, replace } = useFieldArray<{
  quantity: number;
  partBatchId?: string;
}>("partBatchs");

function handleRemovePart(idx: number) {
  if (fields.value.length === 1) {
    return ElMessage.warning("至少保留一条备件信息");
  }
  remove(idx);
}
function handleAddPart() {
  push({
    quantity: 0,
    partBatchId: undefined
  });
}

const attachmentFiles = ref<UploadUserFile[]>([]);
const formData = ref({
  investorType: defineComponentBinds("investorType", elPlusConfig),
  // orgId: defineComponentBinds("orgId", elPlusConfig),
  entryTime: defineComponentBinds("entryTime", elPlusConfig),
  attachments: defineComponentBinds("attachments", elPlusConfig),
  remark: defineComponentBinds("remark", elPlusConfig)
});

const orgList = computed(() => {
  if (formData.value.investorType.modelValue === SpareParts.InvestorType.Hospital) {
    return orgStore.hospitalList;
  } else if (formData.value.investorType.modelValue === SpareParts.InvestorType.Property) {
    return orgStore.departmentList;
  }
});

const {
  state: partBatchOptions,
  execute: getPartBatch,
  isLoading
} = useAsyncState(async (name: string) => {
  const { data } = await getSparePartsBatch(
    {
      pageIndex: 1,
      pageSize: 100,
      name: name ? name : undefined
    },
    false
  );
  return data.records;
}, []);

// 查询详情
let approvalId: string;
const route = useRoute();
const tabStore = useTabsStore();
if (route.name !== "createInboundApproval" && typeof route.params.id === "string") {
  getApprovalDetail(route.params.id).then(({ data }) => {
    console.log(data);
    setValues({
      investorType: data.investorType,
      // orgId: data.orgId,
      entryTime: data.entryTime,
      remark: data.remark
    });
    if (data.partBatchs?.length) {
      replace(data.partBatchs);
    }
    if (data.attachments?.length) {
      attachmentFiles.value = data.attachments;
    }
    approvalId = data.id;
    tabStore.setTabsTitle("编辑审批-" + approvalId);
  });
}

function handleClose() {
  tabStore.removeTabs(route.fullPath);
}

const onSubmit = handleSubmit(async values => {
  console.log("Submitted with", values);
  // debugger;
  const params: SpareParts.ApprovalParams = {
    ...values
  };
  if (attachmentFiles.value.length) {
    params.attachments = attachmentFiles.value.map(item => ({
      name: item.name,
      url: item.url!
    }));
  }

  // if (params.investorType === SpareParts.InvestorType.Property) {
  //   const investor = orgStore.departmentList.find(item => item.id === params.orgId);
  //   params.investor = investor?.name;
  // } else if (params.investorType === SpareParts.InvestorType.Hospital) {
  //   const investor = orgStore.hospitalList.find(item => item.id === params.orgId);
  //   params.investor = investor?.name;
  // }

  console.log(params);
  if (approvalId) {
    await updateApproval({
      ...params,
      id: approvalId
    });
  } else {
    await createApproval(params);
  }
  handleClose();
});
</script>
