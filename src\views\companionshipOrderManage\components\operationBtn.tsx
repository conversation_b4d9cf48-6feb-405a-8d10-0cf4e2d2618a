import {
  CompanionshipOrder,
  auditCompanionshipOrder,
  confirmCompanionshipOrder,
  getCompanionshipOrderDetail,
  orderEnd,
  propertyAuditCompanionshipOrder
} from "@/api/modules/companionshipOrder";
import { Stamp, EditPen, SetUp, View, Tickets, Edit } from "@element-plus/icons-vue";
import { useHandleData } from "@/hooks/useHandleData";
import { useCompanionshipOrderStore, DialogType } from "@/stores/modules/companionshipOrder";
import { ElButton } from "element-plus";

// 操作按钮
interface OperationBtn {
  label: string;
  icon: typeof View;
  vAuth?: string[] | string;
  click: (row: CompanionshipOrder.Record) => void;
}

interface OperationBtnProps {
  row: CompanionshipOrder.Record;
}

export function OperationBtn(props: OperationBtnProps) {
  const orderManageStore = useCompanionshipOrderStore();
  const companionshipOrderManageStore = useCompanionshipOrderStore();
  const router = useRouter();
  /**
   * 查看信息按钮
   */
  const viewBtn = {
    label: "查看信息",
    icon: View,
    click: (row: CompanionshipOrder.Record) => {
      const drawerParams = {
        title: "查看",
        isView: true,
        row: row
      };
      orderManageStore.openDrawer(drawerParams);
    }
  };

  /**
   * 修改陪护信息按钮
   */
  const editBtn: OperationBtn = {
    label: "修改陪护信息",
    icon: EditPen,
    click: (row: CompanionshipOrder.Record) => {
      const drawerParams = {
        title: "编辑",
        isView: false,
        row: row
      };
      orderManageStore.openDrawer(drawerParams);
    }
  };

  /**
   * 院方确认按钮
   */
  const hospitalConfirmBtn: OperationBtn = {
    label: "院方确认",
    icon: Stamp,
    vAuth: "courtyardConfirmButton",
    click: async (params: CompanionshipOrder.Record) => {
      await useHandleData(auditCompanionshipOrder, { orderId: params.id }, `通过【${params.patientName}】的陪护单`);
      companionshipOrderManageStore.getTableList!();
    }
  };

  /**
   * 物业确认按钮
   */
  const strataConfirmBtn: OperationBtn = {
    label: "物业确认",
    icon: Stamp,
    vAuth: "tenementConfirmButton",
    click: async (params: CompanionshipOrder.Record) => {
      await useHandleData(propertyAuditCompanionshipOrder, { orderId: params.id }, `通过【${params.patientName}】的陪护单`);
      companionshipOrderManageStore.getTableList!();
    }
  };

  /**
   * 完善陪护信息按钮
   */
  // export const completeInfoBtn: OperationBtn = {
  //   label: "完善陪护信息",
  //   icon: Edit,
  //   click: (row: CompanionshipOrder.Record) => toDetail("nurseEdit", row)
  // };

  /**
   * 设置付费方式按钮
   */
  const setPaymentMethodBtn: OperationBtn = {
    label: "设置付费方式",
    icon: Edit,
    click: async (row: CompanionshipOrder.Record) => {
      const res = await getCompanionshipOrderDetail(row.id);
      companionshipOrderManageStore.dialogInfo = {
        dialogVisible: DialogType.PayType,
        info: res.data
      };
    }
  };

  /**
   * 修改服务套餐
   */
  const editServicePackageBtn: OperationBtn = {
    label: "修改服务套餐",
    icon: SetUp,
    click: async (row: CompanionshipOrder.Record) => {
      companionshipOrderManageStore.dialogInfo = {
        dialogVisible: DialogType.ServerPackage,
        info: row
      };
    }
  };

  /**
   * 指派护工按钮
   */
  const setNurseBtn: OperationBtn = {
    label: "指派护工",
    icon: SetUp,
    click: async (row: CompanionshipOrder.Record) => {
      const { data } = await getCompanionshipOrderDetail(row.id);
      companionshipOrderManageStore.dialogInfo = {
        dialogVisible: DialogType.Nurse,
        info: {
          // nursingId: data.preNursing?.id || data.curNursing?.id,
          nursingList: data.preNursingList || data.curNursingList,
          time: data.preNursing?.time ? data.preNursing.time : undefined,
          orderId: row.id,
          orderStatus: row.orderStatus
        }
      };
    }
  };

  /**
   * 确认陪护单
   */
  const confirmBtn: OperationBtn = {
    label: "确认陪护单",
    icon: Stamp,
    click: async (params: CompanionshipOrder.Record) => {
      await useHandleData(confirmCompanionshipOrder, { orderId: params.id }, `通过【${params.patientName}】的陪护单`);
      companionshipOrderManageStore.getTableList!();
    }
  };

  /**
   * 设置折扣按钮
   */
  const setDiscountBtn: OperationBtn = {
    label: "设置折扣",
    icon: SetUp,
    click: async (row: CompanionshipOrder.Record) => {
      companionshipOrderManageStore.dialogInfo = {
        dialogVisible: DialogType.Discount,
        info: row
      };
    }
  };

  /**
   * 设置分成按钮
   */
  const setDivideBtn: OperationBtn = {
    label: "设置分成",
    icon: SetUp,
    click: async (row: CompanionshipOrder.Record) => {
      companionshipOrderManageStore.dialogInfo = {
        dialogVisible: DialogType.ShareRatio,
        info: row
      };
    }
  };

  /**
   * 查看护理情况按钮
   */
  // export const checkNursingConditionBtn: OperationBtn = {
  //   label: "查看护理情况",
  //   icon: Tickets,
  //   click: (row: CompanionshipOrder.Record) => toDetail("nurseEdit", row)
  // };
  /**
   * 查看费用明细按钮--每日分成
   */
  const viewExpensesBtn: OperationBtn = {
    label: "查看费用明细",
    icon: Tickets,
    click: (row: CompanionshipOrder.Record) => {
      router.push({ name: "dailyProfitSharing", query: { orderId: row.id } });
    }
  };

  /**
   * 查看评价按钮
   */
  const viewAppraiseBtn: OperationBtn = {
    label: "查看评价",
    icon: Tickets,
    click: (row: CompanionshipOrder.Record) => {
      router.push({ name: "orderEvaluation", query: { orderId: row.id } });
    }
  };

  /**
   * 提前完结按钮
   */
  const confirmEndBtn: OperationBtn = {
    label: "确认提前完结",
    icon: Stamp,
    click: async (params: CompanionshipOrder.Record) => {
      // await useHandleData(orderEnd, { orderId: params.id }, `确认提前结束【${params.patientName}】的陪护单吗`);
      try {
        await ElMessageBox.confirm(`提前结束【${params.patientName}】的陪护单`, "温馨提示", {
          confirmButtonText: "确定",
          cancelButtonText: "驳回",
          type: "warning",
          draggable: true
        });
        await orderEnd({ orderId: params.id, settleApplyOperate: 1 });
      } catch (err: unknown) {
        if (err === "cancel") {
          await orderEnd({ orderId: params.id, settleApplyOperate: 0 });
        } else {
          throw err;
        }
      }
      companionshipOrderManageStore.getTableList!();
    }
  };

  /**
   * 查看资金明细按钮--账单列表
   */
  const viewCapitalDetailsBtn: OperationBtn = {
    label: "查看资金明细",
    icon: Tickets,
    click: (row: CompanionshipOrder.Record) => {
      router.push({ name: "billingList", query: { orderId: row.id } });
    }
  };

  /**
   * 查看发票信息
   */
  // const viewInvoiceInfoBtn: OperationBtn = {
  //   label: "查看发票信息",
  //   icon: Ticket,
  //   click: (row: CompanionshipOrder.Record) => toDetail("nurseEdit", row)
  // };

  const _operationBtn: [CompanionshipOrder.OrderStatus, OperationBtn[]][] = [
    [CompanionshipOrder.OrderStatus.BEndReview, [viewBtn, editBtn, hospitalConfirmBtn]],
    [CompanionshipOrder.OrderStatus.PendingPropertyConfirmation, [viewBtn, editBtn, strataConfirmBtn]],
    [
      CompanionshipOrder.OrderStatus.PendingInformationCompletion,
      [viewBtn, editBtn, setPaymentMethodBtn, editServicePackageBtn, setNurseBtn, confirmBtn, setDiscountBtn, setDivideBtn]
    ],
    [
      CompanionshipOrder.OrderStatus.User,
      [viewBtn, editBtn, setPaymentMethodBtn, editServicePackageBtn, setNurseBtn, setDiscountBtn, setDivideBtn]
    ],
    [CompanionshipOrder.OrderStatus.Nursing, [viewBtn, editBtn, setNurseBtn, setDiscountBtn, setDivideBtn, viewExpensesBtn]],
    [CompanionshipOrder.OrderStatus.SettlementApplication, [viewBtn, confirmEndBtn]],
    [CompanionshipOrder.OrderStatus.Cancelled, [viewBtn]],
    [CompanionshipOrder.OrderStatus.Closed, [viewBtn]],
    [CompanionshipOrder.OrderStatus.Completed, [viewBtn, viewAppraiseBtn, viewCapitalDetailsBtn]]
  ];

  const operationBtn = new Map<CompanionshipOrder.OrderStatus, OperationBtn[]>(_operationBtn);

  const row = props.row;
  const btns = operationBtn.get(row.orderStatus);
  return btns?.map(({ label, click, icon, vAuth }) => {
    if (vAuth)
      return (
        <ElButton type="primary" link icon={icon} onClick={() => click(row)} {...({ vAuth } as any)}>
          {label}
        </ElButton>
      );
    else
      return (
        <ElButton type="primary" link icon={icon} onClick={() => click(row)}>
          {label}
        </ElButton>
      );
  });
}
