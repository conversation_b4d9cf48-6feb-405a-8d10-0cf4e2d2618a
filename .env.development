# 本地环境
VITE_USER_NODE_ENV = development

# 公共基础路径
VITE_PUBLIC_PATH = /

# 路由模式
# Optional: hash | history
VITE_ROUTER_MODE = history

# 打包时是否删除 console
VITE_DROP_CONSOLE = true

# 是否开启 VitePWA
VITE_PWA = false

# 开发环境接口地址
VITE_API_URL = /api

VITE_PORT

# 开发环境跨域代理，支持配置多个
# VITE_PROXY = [["/api","https://mock.mengxuegu.com/mock/629d727e6163854a32e8307e"]]
VITE_PROXY = [["/api","http://127.0.0.1:8181/api"]]

# VITE_PROXY = [["/api-easymock","https://mock.mengxuegu.com"],["/api-fastmock","https://www.fastmock.site"]]
