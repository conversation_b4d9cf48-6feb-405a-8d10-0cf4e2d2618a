<template>
  <div class="table-box">
    <ProTable ref="proTable" :columns="columns" :request-api="getCompanionshipOrderList">
      <!-- 表格 header 按钮 -->
      <template #tableHeader v-if="route.name !== 'companionshipOrderHospitalManage'">
        <el-button type="primary" :icon="CirclePlus" @click="handleAdd">新增陪护单</el-button>
      </template>
      <template #operation="scope" v-if="route.name === 'companionshipOrderHospitalManage'">
        <el-button type="primary" link :icon="View" @click="handleHospitalView(scope.row)"> 查看信息 </el-button>
      </template>
      <template #operation="scope" v-else>
        <OperationBtn :row="scope.row"></OperationBtn>
        <el-button
          v-if="scope.row.orderStatus === CompanionshipOrder.OrderStatus.BEndReview && scope.row.itemIdList.length === 0"
          type="primary"
          link
          :icon="SetUp"
          @click="setServicePackageBtn(scope.row)"
        >
          设置套餐
        </el-button>
      </template>
    </ProTable>
    <DetailDrawer />
    <PayTypeDialog />
    <EditNurseDialog />
    <ServerPackageDialog />
    <DiscountDialog />
    <ShareRatioDialog />
    <DetailHospitalDrawer ref="detailHospitalDrawer" />
  </div>
</template>

<script setup lang="tsx" name="companionshipOrderManage">
import { CompanionshipOrder, getCompanionshipOrderList, orderStatusOptions } from "@/api/modules/companionshipOrder";
import dayjs from "dayjs";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { CirclePlus, View, SetUp } from "@element-plus/icons-vue";
import { genderType, serverTypeOptions } from "@/utils/dict";
import { DialogType, useCompanionshipOrderStore } from "@/stores/modules/companionshipOrder";
import { OperationBtn } from "./components/operationBtn";
import DetailDrawer from "./components/DetailDrawer.vue";
import PayTypeDialog from "./components/PayTypeDialog.vue";
import EditNurseDialog from "./components/EditNurseDialog.vue";
import ServerPackageDialog from "./components/ServicePackageDialog.vue";
import DiscountDialog from "./components/DiscountDialog.vue";
import ShareRatioDialog from "./components/ShareRatioDialog.vue";
import DetailHospitalDrawer from "./components/DetailHospitalDrawer.vue";
import { useOrgStore } from "@/stores/modules/orgId";

const orderManageStore = useCompanionshipOrderStore();

const route = useRoute();

/**
 * proTable 实例
 */
const proTable = ref<ProTableInstance>();

onMounted(() => {
  orderManageStore.getTableList = proTable.value?.getTableList;
});

/**
 * 医院列表
 */
const orgStore = useOrgStore();
const orgList = toRef(() => orgStore.hospitalList.map(item => ({ label: item.name, value: item.id })));

/**
 * 查询列表参数
 */
const columns = reactive<ColumnProps<CompanionshipOrder.Record>[]>([
  // { type: "selection", fixed: "left", width: 70 },
  {
    prop: "id",
    label: "编号",
    width: 180,
    search: { el: "input" }
  },
  {
    prop: "serverType",
    label: "陪护类型",
    width: 100,
    tag: true,
    enum: serverTypeOptions
  },
  {
    prop: "orderStatus",
    label: "陪护单状态",
    width: 180,
    tag: true,
    search: { el: "select" },
    enum: orderStatusOptions
  },
  {
    prop: "patientName",
    label: "病人姓名",
    width: 180
  },
  {
    prop: "patientGender",
    label: "病人性别",
    width: 100,
    isShow: route.name !== "companionshipOrderHospitalManage",
    enum: genderType
  },
  {
    prop: "patientAge",
    label: "病人年龄",
    width: 100,
    isShow: route.name !== "companionshipOrderHospitalManage",
    render: scope => {
      const age = dayjs().diff(dayjs(scope.row.patientBirthday), "year");
      return `${age}岁`;
    }
  },
  {
    prop: "phone",
    width: 180,
    isShow: route.name !== "companionshipOrderHospitalManage",
    label: "家属联系电话"
  },
  {
    prop: "orgId",
    label: "医院",
    width: 300,
    enum: orgList
    // render: scope => {
    //   const org = orgList.value.find(item => item.id === scope.row.orgId);
    //   if (org?.name) {
    //     return org?.name;
    //   }
    //   return "--";
    // }
  },
  {
    prop: "startTime",
    label: "预计开始陪护时间",
    width: 180,
    render: scope => dayjs(Number(scope.row.startTime)).format("YYYY-MM-DD HH:mm")
  },
  {
    prop: "realStartTime",
    label: "实际开始陪护时间",
    width: 180,
    render: scope => dayjs(Number(scope.row.realStartTime)).format("YYYY-MM-DD HH:mm")
  },
  {
    prop: "endTime",
    label: "预计结束陪护时间",
    width: 180,
    render: scope => dayjs(Number(scope.row.endTime)).format("YYYY-MM-DD HH:mm")
  },
  {
    prop: "realEndTime",
    label: "实际结束陪护时间",
    width: 180,
    render: scope => dayjs(Number(scope.row.realEndTime)).format("YYYY-MM-DD HH:mm")
  },
  {
    prop: "operation",
    label: "操作",
    width: 300,
    fixed: "right"
  }
]);
const companionshipOrderManageStore = useCompanionshipOrderStore();

async function setServicePackageBtn(row: CompanionshipOrder.Record) {
  companionshipOrderManageStore.dialogInfo = {
    dialogVisible: DialogType.ServerPackage,
    info: row
  };
}

// 打开 drawer(新增)
function handleAdd() {
  const drawerParams = {
    title: "新增",
    isView: false,
    row: undefined
  };

  orderManageStore.openDrawer(drawerParams);
}

const detailHospitalDrawer = ref<InstanceType<typeof DetailHospitalDrawer> | null>();
/// 查看信息
const handleHospitalView = (row: CompanionshipOrder.Record) => {
  const drawerParams = {
    title: "查看-院方",
    isView: true,
    row: row
  };
  detailHospitalDrawer.value?.acceptParams(drawerParams);
};
</script>
